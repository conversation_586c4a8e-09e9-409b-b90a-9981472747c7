import messaging from '@react-native-firebase/messaging';
import {NavigationContainerRef} from '@react-navigation/native';
import {Platform} from 'react-native';
import AppInstance from '../config/global.axios';

let navigationRef: NavigationContainerRef<any> | null = null;

export const setNavigationRef = (ref: NavigationContainerRef<any>) => {
  navigationRef = ref;
};

export const setupFirebaseMessaging = async () => {


  // Request permission for iOS
  if (Platform.OS === 'ios') {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      console.log('Permission not granted for notifications');
      return;
    }
  }

  // Get FCM token
  try {
    const token = await messaging().getToken();
    console.log('FCM Token:', token);

    // Send token to backend (you can implement this API call)
    // await sendTokenToBackend(token);

    return token;
  } catch (error) {
    console.error('Error getting FCM token:', error);
  }

  // // Handle foreground messages
  // const unsubscribeForeground = messaging().onMessage(async remoteMessage => {
  //   console.log('Foreground message received:', remoteMessage);

  //   if (remoteMessage.data?.type === 'chat_message') {
  //     // Show notification for chat message in foreground (optional)
  //     console.log
  //     (
  //       remoteMessage.notification?.title || 'New Message',
  //       remoteMessage.notification?.body || 'You have a new chat message',
  //       // {
  //       //   onPress: () => handleChatNotification(remoteMessage.data),
  //       // }
  //     );
  //   }
  // });

  // Handle background/quit state messages
  messaging().onNotificationOpenedApp(remoteMessage => {
    console.log('Notification opened app from background:', remoteMessage);
    
    // if (remoteMessage.data?.type === 'chat_message') {
    //   handleChatNotification(remoteMessage.data);
    // }
  });

  // Handle app opened from quit state
  messaging()
    .getInitialNotification()
    .then(remoteMessage => {
      if (remoteMessage) {
        console.log('Notification opened app from quit state:', remoteMessage);
        
        // if (remoteMessage.data?.type === 'chat_message') {
        //   // Delay navigation to ensure app is fully loaded
        //   setTimeout(() => {
        //     handleChatNotification(remoteMessage.data);
        //   }, 2000);
        // }
      }
    });

  // return unsubscribeForeground;
};

const handleChatNotification = (data: any) => {
  if (!navigationRef) {
    console.log('Navigation ref not available');
    return;
  }

  try {
    const chatId = data.chatId;
    const recipient = data.recipient ? JSON.parse(data.recipient) : undefined;

    // Navigate to ChatDetails screen
    navigationRef.navigate('ChatDetails', {
      chatId,
      recipient,
    });
  } catch (error) {
    console.error('Error handling chat notification:', error);
  }
};





const sendTokenToBackend = async (token: string) => {
  try {
    // Send FCM token to your backend server
    // You can implement this API endpoint on your backend
    await AppInstance.post('/user/fcm-token', {
      fcmToken: token,
      platform: Platform.OS,
    });
    console.log('FCM token sent to backend successfully');
  } catch (error) {
    console.error('Error sending FCM token to backend:', error);
  }
};
